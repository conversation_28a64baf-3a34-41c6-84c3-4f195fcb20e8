# Change Log - @ti-platform/aide-vueuse

This log was last generated on Mon, 26 May 2025 15:30:47 GMT and should not be manually modified.

## 1.1.2
Mon, 26 May 2025 15:30:47 GMT

_Version update only_

## 1.1.1
Wed, 09 Apr 2025 20:07:33 GMT

_Version update only_

## 1.1.0
Mon, 07 Apr 2025 14:47:35 GMT

### Minor changes

- Upgrade dependencies; Added tests; Exporting CommonJS as well

## 1.0.13
<PERSON><PERSON>, 25 Feb 2025 01:27:07 GMT

### Patches

- Adding tests

## 1.0.12
Thu, 07 Nov 2024 21:15:00 GMT

_Version update only_

## 1.0.11
Thu, 07 Nov 2024 20:48:29 GMT

### Patches

- Upgrading dependencies

## 1.0.10
Thu, 26 Sep 2024 20:30:17 GMT

_Version update only_

## 1.0.9
Thu, 26 Sep 2024 15:49:21 GMT

_Version update only_

## 1.0.8
Wed, 24 Jul 2024 18:55:35 GMT

_Version update only_

## 1.0.7
Thu, 16 May 2024 18:49:59 GMT

### Patches

- Adding site info.

## 1.0.6
Thu, 16 May 2024 18:34:08 GMT

### Patches

- Improve documentation.

## 1.0.5
Mon, 08 Apr 2024 18:08:17 GMT

_Version update only_

## 1.0.4
Thu, 04 Apr 2024 15:51:52 GMT

_Version update only_

## 1.0.3
Mon, 01 Apr 2024 01:31:07 GMT

_Version update only_

## 1.0.2
Fri, 14 Jul 2023 20:31:34 GMT

_Version update only_

## 1.0.1
Fri, 14 Jul 2023 20:03:39 GMT

_Version update only_

## 1.0.0
Wed, 12 Jul 2023 20:01:30 GMT

### Breaking changes

- Targeting ES2020 as well as changing the exported file types.

## 0.0.7
Mon, 10 Jul 2023 10:01:02 GMT

_Version update only_

## 0.0.6
Thu, 22 Jun 2023 19:39:21 GMT

### Patches

- Upgrading configurations

## 0.0.5
Fri, 24 Mar 2023 14:42:12 GMT

_Version update only_

## 0.0.4
Fri, 17 Feb 2023 17:13:09 GMT

_Version update only_

## 0.0.3
Tue, 15 Nov 2022 19:59:59 GMT

_Version update only_

## 0.0.2
Wed, 19 Oct 2022 18:53:48 GMT

### Patches

- Moving useHeightCalc from @ti-platform/vue to here; Adding ability to poll for data

