{"name": "@ti-platform/aide-vueuse", "entries": [{"version": "1.1.2", "tag": "@ti-platform/aide-vueuse_v1.1.2", "date": "Mon, 26 May 2025 15:30:47 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.7.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.2.2`"}]}}, {"version": "1.1.1", "tag": "@ti-platform/aide-vueuse_v1.1.1", "date": "Wed, 09 Apr 2025 20:07:33 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.6.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.2.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `4.1.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.2.1`"}]}}, {"version": "1.1.0", "tag": "@ti-platform/aide-vueuse_v1.1.0", "date": "Mon, 07 Apr 2025 14:47:35 GMT", "comments": {"minor": [{"comment": "Upgrade dependencies; Added tests; Exporting CommonJS as well"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.6.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.2.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `4.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.2.0`"}]}}, {"version": "1.0.13", "tag": "@ti-platform/aide-vueuse_v1.0.13", "date": "<PERSON><PERSON>, 25 Feb 2025 01:27:07 GMT", "comments": {"patch": [{"comment": "Adding tests"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.5.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.1.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.1.0`"}]}}, {"version": "1.0.12", "tag": "@ti-platform/aide-vueuse_v1.0.12", "date": "Thu, 07 Nov 2024 21:15:00 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.4.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.0.1`"}]}}, {"version": "1.0.11", "tag": "@ti-platform/aide-vueuse_v1.0.11", "date": "Thu, 07 Nov 2024 20:48:29 GMT", "comments": {"patch": [{"comment": "Upgrading dependencies"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.3.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.3.0`"}]}}, {"version": "1.0.10", "tag": "@ti-platform/aide-vueuse_v1.0.10", "date": "Thu, 26 Sep 2024 20:30:17 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.10`"}]}}, {"version": "1.0.9", "tag": "@ti-platform/aide-vueuse_v1.0.9", "date": "Thu, 26 Sep 2024 15:49:21 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.3`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.9`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.2.0`"}]}}, {"version": "1.0.8", "tag": "@ti-platform/aide-vueuse_v1.0.8", "date": "Wed, 24 Jul 2024 18:55:35 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.8`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.3`"}]}}, {"version": "1.0.7", "tag": "@ti-platform/aide-vueuse_v1.0.7", "date": "Thu, 16 May 2024 18:49:59 GMT", "comments": {"patch": [{"comment": "Adding site info."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.7`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.2`"}]}}, {"version": "1.0.6", "tag": "@ti-platform/aide-vueuse_v1.0.6", "date": "Thu, 16 May 2024 18:34:08 GMT", "comments": {"patch": [{"comment": "Improve documentation."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.6`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.1`"}]}}, {"version": "1.0.5", "tag": "@ti-platform/aide-vueuse_v1.0.5", "date": "Mon, 08 Apr 2024 18:08:17 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.1.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.5`"}]}}, {"version": "1.0.4", "tag": "@ti-platform/aide-vueuse_v1.0.4", "date": "Thu, 04 Apr 2024 15:51:52 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.0.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.4`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.0`"}]}}, {"version": "1.0.3", "tag": "@ti-platform/aide-vueuse_v1.0.3", "date": "Mon, 01 Apr 2024 01:31:07 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.3`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.0.0`"}]}}, {"version": "1.0.2", "tag": "@ti-platform/aide-vueuse_v1.0.2", "date": "Fri, 14 Jul 2023 20:31:34 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `2.0.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.1.1`"}]}}, {"version": "1.0.1", "tag": "@ti-platform/aide-vueuse_v1.0.1", "date": "Fri, 14 Jul 2023 20:03:39 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `2.0.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.1.0`"}]}}, {"version": "1.0.0", "tag": "@ti-platform/aide-vueuse_v1.0.0", "date": "Wed, 12 Jul 2023 20:01:30 GMT", "comments": {"major": [{"comment": "Targeting ES2020 as well as changing the exported file types."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `2.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.0.0`"}]}}, {"version": "0.0.7", "tag": "@ti-platform/aide-vueuse_v0.0.7", "date": "Mon, 10 Jul 2023 10:01:02 GMT", "comments": {"none": [{"comment": "Using aliases for relative imports."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.2.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `1.3.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `1.0.0`"}]}}, {"version": "0.0.6", "tag": "@ti-platform/aide-vueuse_v0.0.6", "date": "Thu, 22 Jun 2023 19:39:21 GMT", "comments": {"patch": [{"comment": "Upgrading configurations"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.1.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `1.3.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.11.0`"}]}}, {"version": "0.0.5", "tag": "@ti-platform/aide-vueuse_v0.0.5", "date": "Fri, 24 Mar 2023 14:42:12 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.1.0`"}]}}, {"version": "0.0.4", "tag": "@ti-platform/aide-vueuse_v0.0.4", "date": "Fri, 17 Feb 2023 17:13:09 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.0.0`"}]}}, {"version": "0.0.3", "tag": "@ti-platform/aide-vueuse_v0.0.3", "date": "<PERSON><PERSON>, 15 Nov 2022 19:59:59 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.4.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.10.0`"}]}}, {"version": "0.0.2", "tag": "@ti-platform/aide-vueuse_v0.0.2", "date": "Wed, 19 Oct 2022 18:53:48 GMT", "comments": {"patch": [{"comment": "Moving useHeightCalc from @ti-platform/vue to here; Adding ability to poll for data"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.4.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.9.0`"}]}}]}