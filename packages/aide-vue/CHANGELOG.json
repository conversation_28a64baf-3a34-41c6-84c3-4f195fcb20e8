{"name": "@ti-platform/aide-vue", "entries": [{"version": "3.2.2", "tag": "@ti-platform/aide-vue_v3.2.2", "date": "Mon, 26 May 2025 15:30:47 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.7.0`"}]}}, {"version": "3.2.1", "tag": "@ti-platform/aide-vue_v3.2.1", "date": "Wed, 09 Apr 2025 20:07:33 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.6.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `4.1.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.2.1`"}]}}, {"version": "3.2.0", "tag": "@ti-platform/aide-vue_v3.2.0", "date": "Mon, 07 Apr 2025 14:47:35 GMT", "comments": {"minor": [{"comment": "Upgrade dependencies; Added tests; Exporting CommonJS as well"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.6.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `4.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.2.0`"}]}}, {"version": "3.1.0", "tag": "@ti-platform/aide-vue_v3.1.0", "date": "<PERSON><PERSON>, 25 Feb 2025 01:27:07 GMT", "comments": {"minor": [{"comment": "Add function asRef and tests"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.5.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.1.0`"}]}}, {"version": "3.0.1", "tag": "@ti-platform/aide-vue_v3.0.1", "date": "Thu, 07 Nov 2024 21:15:00 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.4.0`"}]}}, {"version": "3.0.0", "tag": "@ti-platform/aide-vue_v3.0.0", "date": "Thu, 07 Nov 2024 20:48:29 GMT", "comments": {"major": [{"comment": "Upgrading dependencies; Changing return values of Queue to mostly be ComputedRef instead of Ref"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.3.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.3.0`"}]}}, {"version": "2.0.10", "tag": "@ti-platform/aide-vue_v2.0.10", "date": "Thu, 26 Sep 2024 20:30:17 GMT", "comments": {"patch": [{"comment": "Bug fix to allow references injection to work."}]}}, {"version": "2.0.9", "tag": "@ti-platform/aide-vue_v2.0.9", "date": "Thu, 26 Sep 2024 15:49:21 GMT", "comments": {"none": [{"comment": "Moving some things around."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.3`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.2.0`"}]}}, {"version": "2.0.8", "tag": "@ti-platform/aide-vue_v2.0.8", "date": "Wed, 24 Jul 2024 18:55:35 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.3`"}]}}, {"version": "2.0.7", "tag": "@ti-platform/aide-vue_v2.0.7", "date": "Thu, 16 May 2024 18:49:59 GMT", "comments": {"patch": [{"comment": "Adding site info."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.2`"}]}}, {"version": "2.0.6", "tag": "@ti-platform/aide-vue_v2.0.6", "date": "Thu, 16 May 2024 18:34:08 GMT", "comments": {"patch": [{"comment": "Improve documentation."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.1`"}]}}, {"version": "2.0.5", "tag": "@ti-platform/aide-vue_v2.0.5", "date": "Mon, 08 Apr 2024 18:08:17 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.1.0`"}]}}, {"version": "2.0.4", "tag": "@ti-platform/aide-vue_v2.0.4", "date": "Thu, 04 Apr 2024 15:51:52 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.0.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.0`"}]}}, {"version": "2.0.3", "tag": "@ti-platform/aide-vue_v2.0.3", "date": "Mon, 01 Apr 2024 01:31:07 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.0.0`"}]}}, {"version": "2.0.2", "tag": "@ti-platform/aide-vue_v2.0.2", "date": "Fri, 14 Jul 2023 20:31:34 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `2.0.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.1.1`"}]}}, {"version": "2.0.1", "tag": "@ti-platform/aide-vue_v2.0.1", "date": "Fri, 14 Jul 2023 20:03:39 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `2.0.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.1.0`"}]}}, {"version": "2.0.0", "tag": "@ti-platform/aide-vue_v2.0.0", "date": "Wed, 12 Jul 2023 20:01:30 GMT", "comments": {"major": [{"comment": "Targeting ES2020 as well as changing the exported file types."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `2.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.0.0`"}]}}, {"version": "1.3.1", "tag": "@ti-platform/aide-vue_v1.3.1", "date": "Mon, 10 Jul 2023 10:01:02 GMT", "comments": {"none": [{"comment": "Using aliases for relative imports."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.2.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `1.0.0`"}]}}, {"version": "1.3.0", "tag": "@ti-platform/aide-vue_v1.3.0", "date": "Thu, 22 Jun 2023 19:39:21 GMT", "comments": {"minor": [{"comment": "Updating configurations and adding method to return computed Refs"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.1.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.11.0`"}]}}, {"version": "1.2.0", "tag": "@ti-platform/aide-vue_v1.2.0", "date": "Fri, 05 May 2023 13:27:11 GMT", "comments": {"minor": [{"comment": "Adding Ref to the total number of tasks that is running for reactiveExecuteTasks"}]}}, {"version": "1.1.0", "tag": "@ti-platform/aide-vue_v1.1.0", "date": "Fri, 21 Apr 2023 15:44:57 GMT", "comments": {"minor": [{"comment": "Added new function to execute tasks in a queue but with reactive components"}]}}, {"version": "1.0.3", "tag": "@ti-platform/aide-vue_v1.0.3", "date": "Fri, 24 Mar 2023 14:42:12 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.1.0`"}]}}, {"version": "1.0.2", "tag": "@ti-platform/aide-vue_v1.0.2", "date": "Fri, 17 Feb 2023 17:13:09 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.0.0`"}]}}, {"version": "1.0.1", "tag": "@ti-platform/aide-vue_v1.0.1", "date": "<PERSON><PERSON>, 15 Nov 2022 19:59:59 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.4.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.10.0`"}]}}, {"version": "1.0.0", "tag": "@ti-platform/aide-vue_v1.0.0", "date": "Wed, 19 Oct 2022 18:53:48 GMT", "comments": {"major": [{"comment": "Removing useHeightCalc as it is moved to @ti-platform/aide-vueuse"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.4.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.9.0`"}]}}, {"version": "0.3.0", "tag": "@ti-platform/aide-vue_v0.3.0", "date": "Fri, 09 Sep 2022 19:08:12 GMT", "comments": {"minor": [{"comment": "Exporting more types and updating dependencies."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.3.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.8.0`"}]}}, {"version": "0.2.4", "tag": "@ti-platform/aide-vue_v0.2.4", "date": "Mon, 28 Feb 2022 20:13:03 GMT", "comments": {"patch": [{"comment": "Update dependencies versions"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.3.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.4`"}]}}, {"version": "0.2.3", "tag": "@ti-platform/aide-vue_v0.2.3", "date": "Fri, 11 Feb 2022 20:26:17 GMT", "comments": {"patch": [{"comment": "Update code to not use Vue macros"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.2.3`"}]}}, {"version": "0.2.2", "tag": "@ti-platform/aide-vue_v0.2.2", "date": "Thu, 03 Feb 2022 19:05:11 GMT", "comments": {"patch": [{"comment": "Add documentation"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.2.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.3`"}]}}, {"version": "0.2.1", "tag": "@ti-platform/aide-vue_v0.2.1", "date": "Thu, 03 Feb 2022 18:27:49 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.2.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.2`"}]}}, {"version": "0.2.0", "tag": "@ti-platform/aide-vue_v0.2.0", "date": "Thu, 03 Feb 2022 18:17:38 GMT", "comments": {"minor": [{"comment": "Add method to calculate the height of an element"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.2.0`"}]}}, {"version": "0.1.9", "tag": "@ti-platform/aide-vue_v0.1.9", "date": "<PERSON><PERSON>, 18 Jan 2022 21:27:24 GMT", "comments": {"patch": [{"comment": "Upgrade dependencies"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.1.9`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.1`"}]}}, {"version": "0.1.8", "tag": "@ti-platform/aide-vue_v0.1.8", "date": "Sun, 16 Jan 2022 18:29:48 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.1.8`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.0`"}]}}, {"version": "0.1.7", "tag": "@ti-platform/aide-vue_v0.1.7", "date": "Sun, 16 Jan 2022 18:00:35 GMT", "comments": {"patch": [{"comment": "Fix package.json script"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.1.7`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.6.2`"}]}}, {"version": "0.1.6", "tag": "@ti-platform/aide-vue_v0.1.6", "date": "Sun, 16 Jan 2022 15:01:33 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.1.6`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.6.1`"}]}}, {"version": "0.1.5", "tag": "@ti-platform/aide-vue_v0.1.5", "date": "Sun, 16 Jan 2022 14:12:56 GMT", "comments": {"patch": [{"comment": "Build directly with tsc compiler"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.1.5`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.6.0`"}]}}, {"version": "0.1.4", "tag": "@ti-platform/aide-vue_v0.1.4", "date": "Thu, 13 Jan 2022 01:26:40 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.1.4`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.5.0`"}]}}, {"version": "0.1.3", "tag": "@ti-platform/aide-vue_v0.1.3", "date": "Fri, 07 Jan 2022 20:29:38 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.1.3`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.4.0`"}]}}, {"version": "0.1.2", "tag": "@ti-platform/aide-vue_v0.1.2", "date": "Fri, 07 Jan 2022 16:14:19 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.1.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.3.0`"}]}}, {"version": "0.1.1", "tag": "@ti-platform/aide-vue_v0.1.1", "date": "Fri, 07 Jan 2022 14:59:03 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.1.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.2.0`"}]}}, {"version": "0.1.0", "tag": "@ti-platform/aide-vue_v0.1.0", "date": "Thu, 06 Jan 2022 16:28:40 GMT", "comments": {"minor": [{"comment": "Initial addition of utility methods to help with validating form inputs"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.1.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.1.0`"}]}}]}