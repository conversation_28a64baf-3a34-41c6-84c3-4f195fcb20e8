# Change Log - @ti-platform/aide-vue

This log was last generated on Mon, 26 May 2025 15:30:47 GMT and should not be manually modified.

## 3.2.2
Mon, 26 May 2025 15:30:47 GMT

_Version update only_

## 3.2.1
Wed, 09 Apr 2025 20:07:33 GMT

_Version update only_

## 3.2.0
Mon, 07 Apr 2025 14:47:35 GMT

### Minor changes

- Upgrade dependencies; Added tests; Exporting CommonJS as well

## 3.1.0
Tue, 25 Feb 2025 01:27:07 GMT

### Minor changes

- Add function asRef and tests

## 3.0.1
Thu, 07 Nov 2024 21:15:00 GMT

_Version update only_

## 3.0.0
Thu, 07 Nov 2024 20:48:29 GMT

### Breaking changes

- Upgrading dependencies; Changing return values of Queue to mostly be ComputedRef instead of Ref

## 2.0.10
Thu, 26 Sep 2024 20:30:17 GMT

### Patches

- Bug fix to allow references injection to work.

## 2.0.9
Thu, 26 Sep 2024 15:49:21 GMT

_Version update only_

## 2.0.8
Wed, 24 Jul 2024 18:55:35 GMT

_Version update only_

## 2.0.7
Thu, 16 May 2024 18:49:59 GMT

### Patches

- Adding site info.

## 2.0.6
Thu, 16 May 2024 18:34:08 GMT

### Patches

- Improve documentation.

## 2.0.5
Mon, 08 Apr 2024 18:08:17 GMT

_Version update only_

## 2.0.4
Thu, 04 Apr 2024 15:51:52 GMT

_Version update only_

## 2.0.3
Mon, 01 Apr 2024 01:31:07 GMT

_Version update only_

## 2.0.2
Fri, 14 Jul 2023 20:31:34 GMT

_Version update only_

## 2.0.1
Fri, 14 Jul 2023 20:03:39 GMT

_Version update only_

## 2.0.0
Wed, 12 Jul 2023 20:01:30 GMT

### Breaking changes

- Targeting ES2020 as well as changing the exported file types.

## 1.3.1
Mon, 10 Jul 2023 10:01:02 GMT

_Version update only_

## 1.3.0
Thu, 22 Jun 2023 19:39:21 GMT

### Minor changes

- Updating configurations and adding method to return computed Refs

## 1.2.0
Fri, 05 May 2023 13:27:11 GMT

### Minor changes

- Adding Ref to the total number of tasks that is running for reactiveExecuteTasks

## 1.1.0
Fri, 21 Apr 2023 15:44:57 GMT

### Minor changes

- Added new function to execute tasks in a queue but with reactive components

## 1.0.3
Fri, 24 Mar 2023 14:42:12 GMT

_Version update only_

## 1.0.2
Fri, 17 Feb 2023 17:13:09 GMT

_Version update only_

## 1.0.1
Tue, 15 Nov 2022 19:59:59 GMT

_Version update only_

## 1.0.0
Wed, 19 Oct 2022 18:53:48 GMT

### Breaking changes

- Removing useHeightCalc as it is moved to @ti-platform/aide-vueuse

## 0.3.0
Fri, 09 Sep 2022 19:08:12 GMT

### Minor changes

- Exporting more types and updating dependencies.

## 0.2.4
Mon, 28 Feb 2022 20:13:03 GMT

### Patches

- Update dependencies versions

## 0.2.3
Fri, 11 Feb 2022 20:26:17 GMT

### Patches

- Update code to not use Vue macros

## 0.2.2
Thu, 03 Feb 2022 19:05:11 GMT

### Patches

- Add documentation

## 0.2.1
Thu, 03 Feb 2022 18:27:49 GMT

_Version update only_

## 0.2.0
Thu, 03 Feb 2022 18:17:38 GMT

### Minor changes

- Add method to calculate the height of an element

## 0.1.9
Tue, 18 Jan 2022 21:27:24 GMT

### Patches

- Upgrade dependencies

## 0.1.8
Sun, 16 Jan 2022 18:29:48 GMT

_Version update only_

## 0.1.7
Sun, 16 Jan 2022 18:00:35 GMT

### Patches

- Fix package.json script

## 0.1.6
Sun, 16 Jan 2022 15:01:33 GMT

_Version update only_

## 0.1.5
Sun, 16 Jan 2022 14:12:56 GMT

### Patches

- Build directly with tsc compiler

## 0.1.4
Thu, 13 Jan 2022 01:26:40 GMT

_Version update only_

## 0.1.3
Fri, 07 Jan 2022 20:29:38 GMT

_Version update only_

## 0.1.2
Fri, 07 Jan 2022 16:14:19 GMT

_Version update only_

## 0.1.1
Fri, 07 Jan 2022 14:59:03 GMT

_Version update only_

## 0.1.0
Thu, 06 Jan 2022 16:28:40 GMT

### Minor changes

- Initial addition of utility methods to help with validating form inputs

