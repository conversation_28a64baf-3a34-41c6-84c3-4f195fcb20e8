{"name": "@ti-platform/aide-vue", "version": "3.2.2", "homepage": "https://github.com/duclet/ti-platform/tree/master/packages/aide-vue", "repository": "https://github.com/duclet/ti-platform", "type": "module", "types": "./dist/index.d.ts", "main": "./dist/index.cjs", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist/", "src/", "CHANGELOG.md", "README.md"], "scripts": {"build:docs": "run-typedoc", "build:src": "tsup", "clean": "rm -rf ./dist", "lint": "run-linter -d ./src -e .ts -g -k -c 'pnpm tsc --noEmit'", "prod": "pnpm clean && pnpm lint && pnpm test -- run --coverage && pnpm build:src 2>&1 && pnpm build:docs", "dev": "pnpm clean && pnpm lint -w -c 'pnpm build:src'", "build": "pnpm prod", "test": "vitest"}, "dependencies": {"@ti-platform/aide": "workspace:*", "vue": "^3.5.13"}, "devDependencies": {"@ti-platform/aide-build-tools": "workspace:*", "@ti-platform/aide-test": "workspace:*", "@types/node": "^22.14.0", "@vitest/coverage-v8": "^3.1.1", "eslint": "^9.23.0", "prettier": "^3.5.3", "tsup": "^8.4.0", "typedoc": "^0.28.1", "typedoc-plugin-markdown": "^4.6.1", "typescript": "^5.8.2", "vitest": "^3.1.1"}}