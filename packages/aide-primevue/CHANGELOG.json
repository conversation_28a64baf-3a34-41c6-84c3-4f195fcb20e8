{"name": "@ti-platform/aide-primevue", "entries": [{"version": "1.0.5", "tag": "@ti-platform/aide-primevue_v1.0.5", "date": "Mon, 26 May 2025 15:30:47 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.7.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.2.2`"}]}}, {"version": "1.0.4", "tag": "@ti-platform/aide-primevue_v1.0.4", "date": "Wed, 09 Apr 2025 20:07:33 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.6.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.2.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `4.1.0`"}]}}, {"version": "1.0.3", "tag": "@ti-platform/aide-primevue_v1.0.3", "date": "Mon, 07 Apr 2025 14:47:35 GMT", "comments": {"patch": [{"comment": "Upgrade dependencies"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.6.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.2.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `4.0.0`"}]}}, {"version": "1.0.2", "tag": "@ti-platform/aide-primevue_v1.0.2", "date": "<PERSON><PERSON>, 25 Feb 2025 01:27:07 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.5.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.1.0`"}]}}, {"version": "1.0.1", "tag": "@ti-platform/aide-primevue_v1.0.1", "date": "Thu, 07 Nov 2024 21:15:00 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.4.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.0.1`"}]}}, {"version": "1.0.0", "tag": "@ti-platform/aide-primevue_v1.0.0", "date": "Thu, 07 Nov 2024 20:48:29 GMT", "comments": {"major": [{"comment": "Upgrading dependencies; Change return value of getSeverity* methods to be undefined when empty"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.3.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.3.0`"}]}}, {"version": "0.1.0", "tag": "@ti-platform/aide-primevue_v0.1.0", "date": "Tu<PERSON>, 29 Oct 2024 16:01:36 GMT", "comments": {"minor": [{"comment": "Adding extended version of MultiSelect component to allow easily setting the toggle all label."}]}}, {"version": "0.0.3", "tag": "@ti-platform/aide-primevue_v0.0.3", "date": "Thu, 26 Sep 2024 20:30:17 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.10`"}]}}, {"version": "0.0.2", "tag": "@ti-platform/aide-primevue_v0.0.2", "date": "Thu, 26 Sep 2024 15:49:21 GMT", "comments": {"none": [{"comment": "Initial release with similar components from aide-quasar and also a layout component."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.3`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.9`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.2.0`"}]}}]}