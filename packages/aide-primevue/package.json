{"name": "@ti-platform/aide-primevue", "version": "1.0.5", "type": "module", "types": "./dist/index.d.ts", "exports": "./dist/index.js", "files": ["dist/", "src/", "CHANGELOG.md", "README.md"], "scripts": {"build:docs": "run-typedoc -i ./src/index.docs.ts -v 2>&1", "build:src": "vite build", "clean": "rm -rf ./dist", "prod": "pnpm clean && pnpm build:src && pnpm build:docs", "dev": "pnpm clean && pnpm build:src --watch", "build": "pnpm prod", "preview": "pnpm vite -c vite-preview.config.ts"}, "dependencies": {"@primevue/core": "^4.3.3", "@primeuix/themes": "^1.0.1", "@s-libs/micro-dash": "^18.0.0", "@ti-platform/aide": "workspace:*", "@ti-platform/aide-vue": "workspace:*", "@vueuse/core": "^13.0.0", "primeicons": "^7.0.0", "primevue": "^4.3.3", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/vite": "^4.1.2", "@ti-platform/aide-build-tools": "workspace:*", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "defu": "^6.1.4", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^4.3.1", "eslint-plugin-json": "^4.0.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue": "^10.0.0", "prettier": "^3.5.3", "tailwindcss": "^4.1.2", "tailwindcss-primeui": "^0.6.1", "typedoc": "^0.28.1", "typedoc-plugin-markdown": "^4.6.1", "typescript": "^5.8.2", "vite": "^6.2.5", "vite-plugin-dts": "^4.5.3", "vue-docgen-cli": "^4.79.0", "vue-tsc": "^2.2.8"}}