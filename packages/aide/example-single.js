// Example usage of the single function
const { single } = require('./dist/index.js');

// Create a factory function that returns an object
const useData = single(() => {
    console.log('Factory function executed!');
    return { 
        timestamp: Date.now(),
        data: 'Hello World'
    };
});

console.log('=== Demonstrating single function ===');

// First call - executes factory function
console.log('\n1. First call:');
const d1 = useData();
console.log('Result:', d1);

// Second call - returns cached result
console.log('\n2. Second call:');
const d2 = useData();
console.log('Result:', d2);
console.log('Same reference?', d1 === d2); // Should be true

// Reset and call again
console.log('\n3. After reset:');
useData.reset();
const d3 = useData();
console.log('Result:', d3);
console.log('Same reference as d1?', d3 === d1); // Should be false

// resetAndCall method
console.log('\n4. Using resetAndCall:');
const d4 = useData.resetAndCall();
console.log('Result:', d4);
console.log('Same reference as d3?', d4 === d3); // Should be false
