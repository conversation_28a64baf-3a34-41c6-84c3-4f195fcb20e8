# Change Log - @ti-platform/aide

This log was last generated on Mon, 26 May 2025 15:30:47 GMT and should not be manually modified.

## 3.7.0
Mon, 26 May 2025 15:30:47 GMT

### Minor changes

- Added groupBy method for MapPlus; Added asynchronous version of Optional

## 3.6.1
Wed, 09 Apr 2025 20:07:33 GMT

_Version update only_

## 3.6.0
Mon, 07 Apr 2025 14:47:35 GMT

### Minor changes

- Upgrade dependencies; Add more tests; Add function to verify array is non-empty

## 3.5.0
<PERSON><PERSON>, 25 Feb 2025 01:27:07 GMT

### Minor changes

- Add a bunch of new methods and adding tests

## 3.4.0
Thu, 07 Nov 2024 21:15:00 GMT

### Minor changes

- Adding method to find a value within MapPlus and getting the entries as an array

## 3.3.0
Thu, 07 Nov 2024 20:48:29 GMT

### Minor changes

- Upgrading dependencies; Adding MapPlus class which extends Map for more useful methods; Added Optional to support not using nulls/undefined

## 3.2.3
Thu, 26 Sep 2024 15:49:21 GMT

_Version update only_

## 3.2.2
Wed, 24 Jul 2024 18:55:35 GMT

_Version update only_

## 3.2.1
Thu, 16 May 2024 18:49:59 GMT

### Patches

- Adding site info.

## 3.2.0
Thu, 16 May 2024 18:34:08 GMT

### Minor changes

- Improve documentation and adding some new utility types.

## 3.1.0
Mon, 08 Apr 2024 18:08:17 GMT

### Minor changes

- Add return type for Queue

## 3.0.1
Thu, 04 Apr 2024 15:51:52 GMT

_Version update only_

## 3.0.0
Mon, 01 Apr 2024 01:31:07 GMT

### Breaking changes

- Update dependencies.
- Remove the function mapHas and the interface GuardedMap.

### Minor changes

- Added the class Deferred and Queue, and the type Awaitable.

## 2.0.2
Fri, 14 Jul 2023 20:31:34 GMT

_Version update only_

## 2.0.1
Fri, 14 Jul 2023 20:03:39 GMT

_Version update only_

## 2.0.0
Wed, 12 Jul 2023 20:01:30 GMT

### Breaking changes

- Targeting ES2020 as well as changing the exported file types.

## 1.2.0
Mon, 10 Jul 2023 10:01:02 GMT

### Minor changes

- Adding some methods and types to help with type guarding maps.

## 1.1.1
Thu, 22 Jun 2023 19:39:21 GMT

### Patches

- Upgrading configurations

## 1.1.0
Fri, 24 Mar 2023 14:42:12 GMT

### Minor changes

- Adding new method to allow executing tasks asynchronously with a maximum number of concurrent workers.

## 1.0.0
Fri, 17 Feb 2023 17:13:09 GMT

### Breaking changes

- Making the default type for package to be ESM and including the file extension in the import so that it meets ESM specifications.

## 0.4.1
Tue, 15 Nov 2022 19:59:59 GMT

_Version update only_

## 0.4.0
Wed, 19 Oct 2022 18:53:48 GMT

### Minor changes

- Adding ability to use as both a ComnmonJS and ESModule library; Adding function to get first defined value

## 0.3.1
Fri, 09 Sep 2022 19:08:12 GMT

### Patches

- Updating documentation and exporting more types.

## 0.3.0
Mon, 28 Feb 2022 20:13:03 GMT

### Minor changes

- Add type to override properties of object that should be readonly

## 0.2.3
Fri, 11 Feb 2022 20:26:17 GMT

### Patches

- Add methods toMap and ensureType

## 0.2.2
Thu, 03 Feb 2022 19:05:11 GMT

### Patches

- Add documentation

## 0.2.1
Thu, 03 Feb 2022 18:27:49 GMT

_Version update only_

## 0.2.0
Thu, 03 Feb 2022 18:17:38 GMT

### Minor changes

- Add method to get first element of array

## 0.1.9
Tue, 18 Jan 2022 21:27:24 GMT

### Patches

- Upgrade dependencies

## 0.1.8
Sun, 16 Jan 2022 18:29:48 GMT

_Version update only_

## 0.1.7
Sun, 16 Jan 2022 18:00:35 GMT

### Patches

- Fix package.json script

## 0.1.6
Sun, 16 Jan 2022 15:01:33 GMT

_Version update only_

## 0.1.5
Sun, 16 Jan 2022 14:12:56 GMT

### Patches

- Build directly with tsc compiler

## 0.1.4
Thu, 13 Jan 2022 01:26:40 GMT

_Version update only_

## 0.1.3
Fri, 07 Jan 2022 20:29:38 GMT

_Version update only_

## 0.1.2
Fri, 07 Jan 2022 16:14:19 GMT

_Version update only_

## 0.1.1
Fri, 07 Jan 2022 14:59:03 GMT

_Version update only_

## 0.1.0
Thu, 06 Jan 2022 16:28:40 GMT

### Minor changes

- Initial addition of some utility methods and types

