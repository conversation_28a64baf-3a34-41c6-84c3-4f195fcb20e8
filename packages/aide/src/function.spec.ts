import { single } from '@src/function';
import { expect } from '@ti-platform/aide-test';
import { describe, test, vi } from 'vitest';

describe('single', () => {
    test('should execute factory function only once and cache result', () => {
        const factory = vi.fn(() => ({ data: 'test' }));
        const singletonFn = single(factory);

        const result1 = singletonFn();
        const result2 = singletonFn();
        const result3 = singletonFn();

        expect(factory).toHaveBeenCalledTimes(1);
        expect(result1).toBe(result2);
        expect(result2).toBe(result3);
        expect(result1).toEqual({ data: 'test' });
    });

    test('should maintain referential equality for cached results', () => {
        const factory = () => ({ timestamp: Date.now() });
        const singletonFn = single(factory);

        const result1 = singletonFn();
        const result2 = singletonFn();

        expect(result1).toBe(result2); // Same reference
        expect(result1 === result2).toBe(true); // Strict equality
    });

    test('should reset cache and allow factory function to execute again', () => {
        const factory = vi.fn(() => ({ counter: Math.random() }));
        const singletonFn = single(factory);

        const result1 = singletonFn();
        expect(factory).toHaveBeenCalledTimes(1);

        singletonFn.reset();
        const result2 = singletonFn();
        expect(factory).toHaveBeenCalledTimes(2);

        expect(result1).not.toBe(result2); // Different references after reset
    });

    test('should reset and call factory function immediately with resetAndCall', () => {
        const factory = vi.fn(() => ({ value: 'new' }));
        const singletonFn = single(factory);

        const result1 = singletonFn();
        expect(factory).toHaveBeenCalledTimes(1);

        const result2 = singletonFn.resetAndCall();
        expect(factory).toHaveBeenCalledTimes(2);

        expect(result1).not.toBe(result2); // Different references
        expect(result2).toEqual({ value: 'new' });
    });

    test('should work with primitive values', () => {
        const factory = vi.fn(() => 42);
        const singletonFn = single(factory);

        const result1 = singletonFn();
        const result2 = singletonFn();

        expect(factory).toHaveBeenCalledTimes(1);
        expect(result1).toBe(42);
        expect(result2).toBe(42);
    });

    test('should work with string values', () => {
        const factory = vi.fn(() => 'hello world');
        const singletonFn = single(factory);

        const result1 = singletonFn();
        const result2 = singletonFn();

        expect(factory).toHaveBeenCalledTimes(1);
        expect(result1).toBe('hello world');
        expect(result2).toBe('hello world');
    });

    test('should work with null and undefined values', () => {
        const nullFactory = vi.fn(() => null);
        const undefinedFactory = vi.fn(() => undefined);

        const nullSingleton = single(nullFactory);
        const undefinedSingleton = single(undefinedFactory);

        expect(nullSingleton()).toBe(null);
        expect(nullSingleton()).toBe(null);
        expect(nullFactory).toHaveBeenCalledTimes(1);

        expect(undefinedSingleton()).toBe(undefined);
        expect(undefinedSingleton()).toBe(undefined);
        expect(undefinedFactory).toHaveBeenCalledTimes(1);
    });

    test('should propagate errors from factory function and not cache them', () => {
        const error = new Error('Factory error');
        const factory = vi.fn(() => {
            throw error;
        });
        const singletonFn = single(factory);

        expect(() => singletonFn()).toThrow('Factory error');
        expect(() => singletonFn()).toThrow('Factory error');
        expect(factory).toHaveBeenCalledTimes(2); // Should retry on each call when error occurs
    });

    test('should cache successful result after previous errors', () => {
        let callCount = 0;
        const factory = vi.fn(() => {
            callCount++;
            if (callCount <= 2) {
                throw new Error('Temporary error');
            }
            return { success: true };
        });
        const singletonFn = single(factory);

        expect(() => singletonFn()).toThrow('Temporary error');
        expect(() => singletonFn()).toThrow('Temporary error');

        const result1 = singletonFn();
        const result2 = singletonFn();

        expect(factory).toHaveBeenCalledTimes(3);
        expect(result1).toBe(result2);
        expect(result1).toEqual({ success: true });
    });

    test('should handle complex object structures', () => {
        const complexObject = {
            nested: {
                array: [1, 2, 3],
                map: new Map([['key', 'value']]),
                set: new Set([1, 2, 3])
            },
            fn: () => 'test'
        };
        const factory = vi.fn(() => complexObject);
        const singletonFn = single(factory);

        const result1 = singletonFn();
        const result2 = singletonFn();

        expect(factory).toHaveBeenCalledTimes(1);
        expect(result1).toBe(result2);
        expect(result1.nested.array).toBe(complexObject.nested.array);
        expect(result1.fn()).toBe('test');
    });

    test('should work with factory functions that return functions', () => {
        const innerFunction = () => 'inner result';
        const factory = vi.fn(() => innerFunction);
        const singletonFn = single(factory);

        const result1 = singletonFn();
        const result2 = singletonFn();

        expect(factory).toHaveBeenCalledTimes(1);
        expect(result1).toBe(result2);
        expect(result1).toBe(innerFunction);
        expect(result1()).toBe('inner result');
    });

    test('should maintain independent caches for different singleton instances', () => {
        const factory1 = vi.fn(() => ({ id: 1 }));
        const factory2 = vi.fn(() => ({ id: 2 }));

        const singleton1 = single(factory1);
        const singleton2 = single(factory2);

        const result1a = singleton1();
        const result2a = singleton2();
        const result1b = singleton1();
        const result2b = singleton2();

        expect(factory1).toHaveBeenCalledTimes(1);
        expect(factory2).toHaveBeenCalledTimes(1);
        expect(result1a).toBe(result1b);
        expect(result2a).toBe(result2b);
        expect(result1a).not.toBe(result2a);
    });
});
