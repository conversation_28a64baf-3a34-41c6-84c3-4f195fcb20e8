{"name": "@ti-platform/aide", "entries": [{"version": "3.7.0", "tag": "@ti-platform/aide_v3.7.0", "date": "Mon, 26 May 2025 15:30:47 GMT", "comments": {"minor": [{"comment": "Added groupBy method for MapPlus; Added asynchronous version of Optional"}]}}, {"version": "3.6.1", "tag": "@ti-platform/aide_v3.6.1", "date": "Wed, 09 Apr 2025 20:07:33 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `4.1.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.2.1`"}]}}, {"version": "3.6.0", "tag": "@ti-platform/aide_v3.6.0", "date": "Mon, 07 Apr 2025 14:47:35 GMT", "comments": {"minor": [{"comment": "Upgrade dependencies; Add more tests; Add function to verify array is non-empty"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `4.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.2.0`"}]}}, {"version": "3.5.0", "tag": "@ti-platform/aide_v3.5.0", "date": "<PERSON><PERSON>, 25 Feb 2025 01:27:07 GMT", "comments": {"minor": [{"comment": "Add a bunch of new methods and adding tests"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.1.0`"}]}}, {"version": "3.4.0", "tag": "@ti-platform/aide_v3.4.0", "date": "Thu, 07 Nov 2024 21:15:00 GMT", "comments": {"minor": [{"comment": "Adding method to find a value within MapPlus and getting the entries as an array"}]}}, {"version": "3.3.0", "tag": "@ti-platform/aide_v3.3.0", "date": "Thu, 07 Nov 2024 20:48:29 GMT", "comments": {"minor": [{"comment": "Upgrading dependencies; Adding MapPlus class which extends Map for more useful methods; Added Optional to support not using nulls/undefined"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.3.0`"}]}}, {"version": "3.2.3", "tag": "@ti-platform/aide_v3.2.3", "date": "Thu, 26 Sep 2024 15:49:21 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.2.0`"}]}}, {"version": "3.2.2", "tag": "@ti-platform/aide_v3.2.2", "date": "Wed, 24 Jul 2024 18:55:35 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.3`"}]}}, {"version": "3.2.1", "tag": "@ti-platform/aide_v3.2.1", "date": "Thu, 16 May 2024 18:49:59 GMT", "comments": {"patch": [{"comment": "Adding site info."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.2`"}]}}, {"version": "3.2.0", "tag": "@ti-platform/aide_v3.2.0", "date": "Thu, 16 May 2024 18:34:08 GMT", "comments": {"minor": [{"comment": "Improve documentation and adding some new utility types."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.1`"}]}}, {"version": "3.1.0", "tag": "@ti-platform/aide_v3.1.0", "date": "Mon, 08 Apr 2024 18:08:17 GMT", "comments": {"minor": [{"comment": "Add return type for Queue"}]}}, {"version": "3.0.1", "tag": "@ti-platform/aide_v3.0.1", "date": "Thu, 04 Apr 2024 15:51:52 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.0`"}]}}, {"version": "3.0.0", "tag": "@ti-platform/aide_v3.0.0", "date": "Mon, 01 Apr 2024 01:31:07 GMT", "comments": {"major": [{"comment": "Update dependencies."}, {"comment": "Remove the function mapHas and the interface GuardedMap."}], "minor": [{"comment": "Added the class Deferred and Queue, and the type Awaitable."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.0.0`"}]}}, {"version": "2.0.2", "tag": "@ti-platform/aide_v2.0.2", "date": "Fri, 14 Jul 2023 20:31:34 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.1.1`"}]}}, {"version": "2.0.1", "tag": "@ti-platform/aide_v2.0.1", "date": "Fri, 14 Jul 2023 20:03:39 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.1.0`"}]}}, {"version": "2.0.0", "tag": "@ti-platform/aide_v2.0.0", "date": "Wed, 12 Jul 2023 20:01:30 GMT", "comments": {"major": [{"comment": "Targeting ES2020 as well as changing the exported file types."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.0.0`"}]}}, {"version": "1.2.0", "tag": "@ti-platform/aide_v1.2.0", "date": "Mon, 10 Jul 2023 10:01:02 GMT", "comments": {"minor": [{"comment": "Adding some methods and types to help with type guarding maps."}], "none": [{"comment": "Using aliases for relative imports."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `1.0.0`"}]}}, {"version": "1.1.1", "tag": "@ti-platform/aide_v1.1.1", "date": "Thu, 22 Jun 2023 19:39:21 GMT", "comments": {"patch": [{"comment": "Upgrading configurations"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.11.0`"}]}}, {"version": "1.1.0", "tag": "@ti-platform/aide_v1.1.0", "date": "Fri, 24 Mar 2023 14:42:12 GMT", "comments": {"minor": [{"comment": "Adding new method to allow executing tasks asynchronously with a maximum number of concurrent workers."}]}}, {"version": "1.0.0", "tag": "@ti-platform/aide_v1.0.0", "date": "Fri, 17 Feb 2023 17:13:09 GMT", "comments": {"major": [{"comment": "Making the default type for package to be ESM and including the file extension in the import so that it meets ESM specifications."}]}}, {"version": "0.4.1", "tag": "@ti-platform/aide_v0.4.1", "date": "<PERSON><PERSON>, 15 Nov 2022 19:59:59 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.10.0`"}]}}, {"version": "0.4.0", "tag": "@ti-platform/aide_v0.4.0", "date": "Wed, 19 Oct 2022 18:53:48 GMT", "comments": {"minor": [{"comment": "Adding ability to use as both a ComnmonJS and ESModule library; Adding function to get first defined value"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.9.0`"}]}}, {"version": "0.3.1", "tag": "@ti-platform/aide_v0.3.1", "date": "Fri, 09 Sep 2022 19:08:12 GMT", "comments": {"patch": [{"comment": "Updating documentation and exporting more types."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.8.0`"}]}}, {"version": "0.3.0", "tag": "@ti-platform/aide_v0.3.0", "date": "Mon, 28 Feb 2022 20:13:03 GMT", "comments": {"minor": [{"comment": "Add type to override properties of object that should be readonly"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.4`"}]}}, {"version": "0.2.3", "tag": "@ti-platform/aide_v0.2.3", "date": "Fri, 11 Feb 2022 20:26:17 GMT", "comments": {"patch": [{"comment": "Add methods toMap and ensureType"}]}}, {"version": "0.2.2", "tag": "@ti-platform/aide_v0.2.2", "date": "Thu, 03 Feb 2022 19:05:11 GMT", "comments": {"patch": [{"comment": "Add documentation"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.3`"}]}}, {"version": "0.2.1", "tag": "@ti-platform/aide_v0.2.1", "date": "Thu, 03 Feb 2022 18:27:49 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.2`"}]}}, {"version": "0.2.0", "tag": "@ti-platform/aide_v0.2.0", "date": "Thu, 03 Feb 2022 18:17:38 GMT", "comments": {"minor": [{"comment": "Add method to get first element of array"}]}}, {"version": "0.1.9", "tag": "@ti-platform/aide_v0.1.9", "date": "<PERSON><PERSON>, 18 Jan 2022 21:27:24 GMT", "comments": {"patch": [{"comment": "Upgrade dependencies"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.1`"}]}}, {"version": "0.1.8", "tag": "@ti-platform/aide_v0.1.8", "date": "Sun, 16 Jan 2022 18:29:48 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.0`"}]}}, {"version": "0.1.7", "tag": "@ti-platform/aide_v0.1.7", "date": "Sun, 16 Jan 2022 18:00:35 GMT", "comments": {"patch": [{"comment": "Fix package.json script"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.6.2`"}]}}, {"version": "0.1.6", "tag": "@ti-platform/aide_v0.1.6", "date": "Sun, 16 Jan 2022 15:01:33 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.6.1`"}]}}, {"version": "0.1.5", "tag": "@ti-platform/aide_v0.1.5", "date": "Sun, 16 Jan 2022 14:12:56 GMT", "comments": {"patch": [{"comment": "Build directly with tsc compiler"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.6.0`"}]}}, {"version": "0.1.4", "tag": "@ti-platform/aide_v0.1.4", "date": "Thu, 13 Jan 2022 01:26:40 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.5.0`"}]}}, {"version": "0.1.3", "tag": "@ti-platform/aide_v0.1.3", "date": "Fri, 07 Jan 2022 20:29:38 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.4.0`"}]}}, {"version": "0.1.2", "tag": "@ti-platform/aide_v0.1.2", "date": "Fri, 07 Jan 2022 16:14:19 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.3.0`"}]}}, {"version": "0.1.1", "tag": "@ti-platform/aide_v0.1.1", "date": "Fri, 07 Jan 2022 14:59:03 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.2.0`"}]}}, {"version": "0.1.0", "tag": "@ti-platform/aide_v0.1.0", "date": "Thu, 06 Jan 2022 16:28:40 GMT", "comments": {"minor": [{"comment": "Initial addition of some utility methods and types"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.1.0`"}]}}]}