{"name": "@ti-platform/client-airtable", "entries": [{"version": "0.3.2", "tag": "@ti-platform/client-airtable_v0.3.2", "date": "Mon, 26 May 2025 15:30:47 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.7.0`"}]}}, {"version": "0.3.1", "tag": "@ti-platform/client-airtable_v0.3.1", "date": "Wed, 09 Apr 2025 20:07:33 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.6.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `4.1.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.2.1`"}]}}, {"version": "0.3.0", "tag": "@ti-platform/client-airtable_v0.3.0", "date": "Mon, 07 Apr 2025 14:47:35 GMT", "comments": {"minor": [{"comment": "Upgrade dependencies; Added tests; Fix bug with createRecords typings"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.6.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `4.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-test\" to `0.2.0`"}]}}, {"version": "0.2.5", "tag": "@ti-platform/client-airtable_v0.2.5", "date": "<PERSON><PERSON>, 25 Feb 2025 01:27:07 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.5.0`"}]}}, {"version": "0.2.4", "tag": "@ti-platform/client-airtable_v0.2.4", "date": "Thu, 07 Nov 2024 21:15:00 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.4.0`"}]}}, {"version": "0.2.3", "tag": "@ti-platform/client-airtable_v0.2.3", "date": "Thu, 07 Nov 2024 20:48:29 GMT", "comments": {"patch": [{"comment": "Upgrading dependencies"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.3.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.3.0`"}]}}, {"version": "0.2.2", "tag": "@ti-platform/client-airtable_v0.2.2", "date": "Thu, 26 Sep 2024 15:49:21 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.3`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.2.0`"}]}}, {"version": "0.2.1", "tag": "@ti-platform/client-airtable_v0.2.1", "date": "Tu<PERSON>, 20 Aug 2024 17:38:35 GMT", "comments": {"patch": [{"comment": "Reverse schema OR definition for updating records so it can actually parse the more specific definition."}]}}, {"version": "0.2.0", "tag": "@ti-platform/client-airtable_v0.2.0", "date": "Wed, 24 Jul 2024 18:55:35 GMT", "comments": {"minor": [{"comment": "Expose some static values as constants and cleanup code."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.3`"}]}}, {"version": "0.1.1", "tag": "@ti-platform/client-airtable_v0.1.1", "date": "Thu, 16 May 2024 18:49:59 GMT", "comments": {"patch": [{"comment": "Adding site info."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.2`"}]}}, {"version": "0.1.0", "tag": "@ti-platform/client-airtable_v0.1.0", "date": "Thu, 16 May 2024 18:34:08 GMT", "comments": {"minor": [{"comment": "Initial release."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.1`"}]}}]}