{
  "$schema": "https://typedoc.org/schema.json",
  "excludeInternal": true,
  "excludePrivate": true,
  "excludeProtected": true,
  "pretty": true,
  "plugin": ["typedoc-plugin-markdown"],
  "outputFileStrategy": "modules",
  "expandObjects": true,
  "parametersFormat": "table",
  "propertiesFormat": "table",
  "enumMembersFormat": "table",
  "typeDeclarationFormat": "table",
  "hidePageHeader": true,
  "hidePageTitle": true,
  "useHTMLEncodedBrackets": true,
  "tableColumnSettings": {
    "hideSources": true
  },
}
