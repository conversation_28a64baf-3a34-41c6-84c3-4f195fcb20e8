{"extends": "@tsconfig/recommended/tsconfig.json", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "allowUnreachableCode": false, "experimentalDecorators": true, "isolatedModules": true, "lib": ["ESNext", "DOM"], "listEmittedFiles": true, "module": "ESNext", "moduleResolution": "bundler", "noImplicitAny": false, "outDir": "dist", "resolveJsonModule": true, "sourceMap": false, "target": "ESNext", "useDefineForClassFields": true}, "exclude": ["node_modules"]}