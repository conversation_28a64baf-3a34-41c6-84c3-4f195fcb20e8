{"name": "@ti-platform/auto-generated-combined-package-json-versions", "lastUpdated": "2025-04-05T23:20:29.102Z", "dependencies": {"@eslint/js": "^9.24.0", "@eslint/json": "^0.11.0", "@html-eslint/eslint-plugin": "^0.37.0", "@html-eslint/parser": "^0.37.0", "@primeuix/themes": "^1.0.1", "@primevue/core": "^4.3.3", "@s-libs/micro-dash": "^18.0.0", "@tailwindcss/vite": "^4.1.2", "@tsconfig/recommended": "^1.0.8", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/coverage-v8": "^3.1.1", "@vueuse/core": "^13.0.0", "chokidar": "^4.0.3", "cleye": "^1.3.4", "defu": "^6.1.4", "dotenv": "^16.4.7", "dotenv-expand": "^12.0.1", "esbuild": "^0.25.2", "esbuild-register": "^3.6.0", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^4.3.1", "eslint-plugin-import-x": "^4.10.0", "eslint-plugin-json": "^4.0.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue": "^10.0.0", "glob": "^11.0.1", "globals": "^16.0.0", "jsdom": "^26.0.0", "ofetch": "^1.4.1", "pkg-types": "^1.2.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "primeicons": "^7.0.0", "primevue": "^4.3.3", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-toc": "^9.0.0", "rollup": "^4.39.0", "tailwindcss": "^4.1.2", "tailwindcss-primeui": "^0.6.1", "ts-debounce": "^4.0.0", "tsconfig-paths": "^4.2.0", "tsup": "^8.4.0", "tsx": "^4.19.3", "type-fest": "^4.39.1", "typedoc": "^0.28.1", "typedoc-plugin-markdown": "^4.6.1", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0", "vite": "^6.2.5", "vite-plugin-dts": "^4.5.3", "vitest": "^3.1.1", "vue": "^3.5.13", "vue-docgen-cli": "^4.79.0", "vue-router": "^4.5.0", "vue-tsc": "^2.2.8", "zod": "^3.24.2"}}