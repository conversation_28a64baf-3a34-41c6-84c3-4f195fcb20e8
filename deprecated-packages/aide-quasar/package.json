{"name": "@ti-platform/aide-quasar", "version": "3.0.13", "homepage": "https://github.com/duclet/ti-platform/tree/master/packages/aide-quasar", "repository": "https://github.com/duclet/ti-platform", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/", "src/", "CHANGELOG.md", "README.md"], "scripts": {"build:docs": "run-typedoc -i ./src/index.docs.ts -v 2>&1", "build:src": "vite build", "clean": "rm -rf ./dist", "prod": "pnpm clean && pnpm build:src && pnpm build:docs", "dev": "pnpm clean && pnpm build:src --watch", "build": "pnpm prod", "preview": "pnpm vite -c vite-preview.config.ts"}, "dependencies": {"@s-libs/micro-dash": "^18.0.0", "@ti-platform/aide": "workspace:*", "@ti-platform/aide-vue": "workspace:*", "@vueuse/core": "^11.2.0", "quasar": "^2.15.2", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@quasar/extras": "^1.16.11", "@quasar/vite-plugin": "^1.6.0", "@ti-platform/aide-build-tools": "workspace:*", "@types/node": "^20.12.2", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "@vitejs/plugin-vue": "^5.1.4", "defu": "^6.1.4", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^4.2.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-json": "^4.0.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue": "^10.0.0", "prettier": "^3.5.3", "sass": "^1.72.0", "typedoc": "^0.28.1", "typedoc-plugin-markdown": "^4.6.0", "typescript": "^5.6.3", "vite": "^5.4.10", "vite-plugin-dts": "^3.8.1", "vue-docgen-cli": "^4.79.0", "vue-tsc": "^2.1.10"}}