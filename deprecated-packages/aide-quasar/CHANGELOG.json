{"name": "@ti-platform/aide-quasar", "entries": [{"version": "3.0.13", "tag": "@ti-platform/aide-quasar_v3.0.13", "date": "<PERSON><PERSON>, 25 Feb 2025 01:27:07 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.5.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.1.0`"}]}}, {"version": "3.0.12", "tag": "@ti-platform/aide-quasar_v3.0.12", "date": "Thu, 07 Nov 2024 21:15:00 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.4.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.0.1`"}]}}, {"version": "3.0.11", "tag": "@ti-platform/aide-quasar_v3.0.11", "date": "Thu, 07 Nov 2024 20:48:29 GMT", "comments": {"none": [{"comment": "Closing out support for package."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.3.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `3.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.3.0`"}]}}, {"version": "3.0.10", "tag": "@ti-platform/aide-quasar_v3.0.10", "date": "Thu, 26 Sep 2024 20:30:17 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.10`"}]}}, {"version": "3.0.9", "tag": "@ti-platform/aide-quasar_v3.0.9", "date": "Thu, 26 Sep 2024 15:49:21 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.3`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.9`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.2.0`"}]}}, {"version": "3.0.8", "tag": "@ti-platform/aide-quasar_v3.0.8", "date": "Wed, 24 Jul 2024 18:55:35 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.8`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.3`"}]}}, {"version": "3.0.7", "tag": "@ti-platform/aide-quasar_v3.0.7", "date": "Thu, 16 May 2024 18:49:59 GMT", "comments": {"patch": [{"comment": "Adding site info."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.7`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.2`"}]}}, {"version": "3.0.6", "tag": "@ti-platform/aide-quasar_v3.0.6", "date": "Thu, 16 May 2024 18:34:08 GMT", "comments": {"patch": [{"comment": "Improve documentation."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.2.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.6`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.1`"}]}}, {"version": "3.0.5", "tag": "@ti-platform/aide-quasar_v3.0.5", "date": "Mon, 08 Apr 2024 18:08:17 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.1.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.5`"}]}}, {"version": "3.0.4", "tag": "@ti-platform/aide-quasar_v3.0.4", "date": "Thu, 04 Apr 2024 15:51:52 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.0.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.4`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.1.0`"}]}}, {"version": "3.0.3", "tag": "@ti-platform/aide-quasar_v3.0.3", "date": "Mon, 01 Apr 2024 01:31:07 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `3.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.3`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `3.0.0`"}]}}, {"version": "3.0.2", "tag": "@ti-platform/aide-quasar_v3.0.2", "date": "Fri, 14 Jul 2023 20:31:34 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `2.0.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.2`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.1.1`"}]}}, {"version": "3.0.1", "tag": "@ti-platform/aide-quasar_v3.0.1", "date": "Fri, 14 Jul 2023 20:03:39 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `2.0.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.1.0`"}]}}, {"version": "3.0.0", "tag": "@ti-platform/aide-quasar_v3.0.0", "date": "Wed, 12 Jul 2023 20:01:30 GMT", "comments": {"major": [{"comment": "Targeting ES2020 as well as changing the exported file types."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `2.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `2.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `2.0.0`"}]}}, {"version": "2.2.3", "tag": "@ti-platform/aide-quasar_v2.2.3", "date": "Mon, 10 Jul 2023 10:01:02 GMT", "comments": {"patch": [{"comment": "Bug fix for TimelineStepper component with its default value being given invalid function reference."}, {"comment": "Exporting as multiple files rather than singular file."}, {"comment": "Fixing Typescript definition exports."}], "none": [{"comment": "Using aliases for relative imports."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.2.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `1.3.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `1.0.0`"}]}}, {"version": "2.2.2", "tag": "@ti-platform/aide-quasar_v2.2.2", "date": "Thu, 22 Jun 2023 19:39:21 GMT", "comments": {"patch": [{"comment": "Upgrading configurations and dependencies"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.1.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `1.3.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.11.0`"}]}}, {"version": "2.2.1", "tag": "@ti-platform/aide-quasar_v2.2.1", "date": "Fri, 05 May 2023 13:27:11 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide-vue\" to `1.2.0`"}]}}, {"version": "2.2.0", "tag": "@ti-platform/aide-quasar_v2.2.0", "date": "Fri, 21 Apr 2023 15:44:57 GMT", "comments": {"minor": [{"comment": "Adding ability to hide or show the navigation bar"}], "patch": [{"comment": "Switching to use VueUse's useStepper utility method to handle the steps interactions"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide-vue\" to `1.1.0`"}]}}, {"version": "2.1.1", "tag": "@ti-platform/aide-quasar_v2.1.1", "date": "Fri, 24 Mar 2023 14:42:12 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.1.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `1.0.3`"}]}}, {"version": "2.1.0", "tag": "@ti-platform/aide-quasar_v2.1.0", "date": "Fri, 17 Feb 2023 17:13:09 GMT", "comments": {"minor": [{"comment": "MultiProgressIndicator: Adding more slots that can be used to customize the display."}, {"comment": "MultiProgressIndicator: Adding a data attribute to each progress item row which can be queried against for further customizations."}, {"comment": "MultiProgressIndicator: The error message now renders inside of a QBtn."}, {"comment": "MultiProgressIndicator: This component is now also exported as MultiProgressIndicatorComponent for consistency with the other components."}, {"comment": "TimelineStepper: Adding ability to initially hide a step until it is started."}, {"comment": "TimelineStepper: This component is now also exported as TimelineStepper for consistency with other components."}, {"comment": "WizardStepper: Adding more slots that can be used to customize the display."}, {"comment": "WizardStepper: The navigational slots now also receives property to let it know when the user is done with the last step."}, {"comment": "WizardStepper: The default template for the navigational slots now hides the continue button when the last step is done and also shows a done button instead."}, {"comment": "WizardStepper: The default template for the navigational slots can now be configured to hide the back button when the user is in the last step and it is done."}, {"comment": "WizardStepper: Modifying execution to only call vue-router related features if it is actually used by user."}, {"comment": "WizardStepper: This component is now also exported as WizardStepper for consistency with other components."}, {"comment": "Adding functionality to preview components during development to help with testing. Note I didn't want to include the huge dependencies of Storybook so just using Vite for this."}], "patch": [{"comment": "TimelineStepper: Fixing bug where the skipped steps before didn't actually skip and ran the task instead. It should now just mark the step as skipped."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `1.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `1.0.2`"}]}}, {"version": "2.0.0", "tag": "@ti-platform/aide-quasar_v2.0.0", "date": "<PERSON><PERSON>, 15 Nov 2022 19:59:59 GMT", "comments": {"major": [{"comment": "Adding the MultiProgressIndicator component; Standardize the TimelineStepper and WizardStepper component public API and usage; Adding much needed documentation"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.4.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `1.0.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.10.0`"}]}}, {"version": "1.0.3", "tag": "@ti-platform/aide-quasar_v1.0.3", "date": "Wed, 19 Oct 2022 18:53:48 GMT", "comments": {"dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.4.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `1.0.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.9.0`"}]}}, {"version": "1.0.2", "tag": "@ti-platform/aide-quasar_v1.0.2", "date": "Fri, 09 Sep 2022 19:08:12 GMT", "comments": {"patch": [{"comment": "Dependencies version updates."}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.3.1`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `0.3.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.8.0`"}]}}, {"version": "1.0.1", "tag": "@ti-platform/aide-quasar_v1.0.1", "date": "Tue, 03 May 2022 18:29:13 GMT", "comments": {"patch": [{"comment": "Fixing bug with properly supporting the back button for WizardStepper component"}]}}, {"version": "1.0.0", "tag": "@ti-platform/aide-quasar_v1.0.0", "date": "Tue, 03 May 2022 16:22:27 GMT", "comments": {"major": [{"comment": "Remove wrapper element from WizardStepper"}]}}, {"version": "0.2.0", "tag": "@ti-platform/aide-quasar_v0.2.0", "date": "Tu<PERSON>, 01 Mar 2022 20:10:41 GMT", "comments": {"minor": [{"comment": "WizardStepperComponent - Add ability to get index of latest step that was viewed"}]}}, {"version": "0.1.0", "tag": "@ti-platform/aide-quasar_v0.1.0", "date": "Mon, 28 Feb 2022 20:13:03 GMT", "comments": {"minor": [{"comment": "Initial addition with components TimelineStepper and WizardStep"}], "dependency": [{"comment": "Updating dependency \"@ti-platform/aide\" to `0.3.0`"}, {"comment": "Updating dependency \"@ti-platform/aide-vue\" to `0.2.4`"}, {"comment": "Updating dependency \"@ti-platform/aide-build-tools\" to `0.7.4`"}]}}]}