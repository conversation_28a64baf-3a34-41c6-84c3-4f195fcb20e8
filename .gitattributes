# Don't allow people to merge changes to these generated files, because the result
# may be invalid.  You need to run "rush update" again.
pnpm-lock.yaml               merge=binary
shrinkwrap.yaml              merge=binary
npm-shrinkwrap.json          merge=binary
yarn.lock                    merge=binary

# <PERSON>'s JSON config files use JavaScript-style code comments.  The rule below prevents pedantic
# syntax highlighters such as GitHub's from highlighting these comments as errors.  Your text editor
# may also require a special configuration to allow comments in JSON.
#
# For more information, see this issue: https://github.com/microsoft/rushstack/issues/1088
#
*.json                       linguist-language=JSON-with-Comments
