Create a new function called "single" or something similar that would allow me to do the following:

const useData = single(() => {
    // Do some work
    return data;
});

const d1 = useData();
const d2 = useData();

d1 === d2; // true

useData.reset();

const d3 = useData();

d3 === d1; // false

const d4 = useData();

d4 === d3; // true

const d5 = useData.resetAndCall();

d5 === d4; // false

Please ensure you follow the coding style of the project.
