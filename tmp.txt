Create a new package called @ti-platform/di. Refer to other packages for how to setup a new one. Also take note of how those packages code are written to follow the same coding style. The package @ti-platform/aide and @ti-platform/client-airtable are probably the best examples. 

This new package you are writing is a dependency injection framework using Typescript. You will not use decorators or reflect-metadata. This should be similar to Java's Spring Boot where the "index" or starting code will create a registry. The registry will then load into itself all data that have been configured to be loaded into the registry. I would like creation of "beans" to be factory functions where it receives an object as its sole argument. Each property of that argument is data that the user configure would be needed to create the "bean". To make the code a little simplier, when registering a bean, the user is required to provide a unique name for the bean and the factory function that will be used to create the bean. 

The layout I expect to see for how configurations would be set is along the lines of:

- configs
  - app.ts
  - database.ts
  - logger.ts
  - server.ts
  - index.ts

In `index.ts`, it, the code would be something along the lines of:

```typescript
import { createRegistry } from '@ti-platform/di';
import { a1, a2 } from './app';
import { d1, d2 } from './database';
import { l1, l2} from './logger';
import { s1, s2 } from './server';

const _registry;

export function useRegistry() {
    if (_registry) {
        return _registry;
    }

    _registry = createRegistry({
        log1: l1,
        log2: l2,
    });
    _registry.register('app1', a1);
    _registry.register('app2', a2);
    _registry.register('database1', d1);
    _registry.register('database2', d2);
    _registry.register('server1', s1);
    _registry.register('server2', s2);

    return _registry;
}
```

Simply registering should not create the instance. There are two ways the instances should be created. First is on-demand. When the user request an instance by name, it will look at 
